
'use client'

import { useState, useEffect } from 'react'
import { motion } from 'framer-motion'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Label } from '@/components/ui/label'
import { Slider } from '@/components/ui/slider'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { BarChart, Bar, XAxis, YAxis, ResponsiveContainer, Cell, Tooltip } from 'recharts'
import { loadPricingData } from '@/lib/data'
import type { PricingData } from '@/lib/types'
import { Calculator, DollarSign, TrendingUp, AlertCircle } from 'lucide-react'

const platformColors = {
  'Harvester': '#60B5FF',
  'OpenShift': '#FF9149',
  'vSphere': '#FF9898'
}

export default function CostCalculator() {
  const [pricingData, setPricingData] = useState<PricingData | null>(null)
  const [hosts, setHosts] = useState([4])
  const [socketsPerHost, setSocketsPerHost] = useState([2])
  const [supportLevel, setSupportLevel] = useState('standard')
  const [loading, setLoading] = useState(true)

  useEffect(() => {
    const fetchData = async () => {
      try {
        const data = await loadPricingData()
        setPricingData(data)
      } catch (error) {
        console.error('Error loading pricing data:', error)
      } finally {
        setLoading(false)
      }
    }
    fetchData()
  }, [])

  const calculateCosts = () => {
    if (!pricingData?.pricing_data) return []

    const totalSockets = hosts[0] * socketsPerHost[0]
    const totalCores = totalSockets * 16 // Assume 16 cores per socket average

    // Harvester - Free with optional support
    const harvesterCost = 0 // Open source, no licensing fees
    const harvesterSupportCost = supportLevel === 'premium' ? 5000 : 2500 // Estimated SUSE support cost per node

    // OpenShift Virtualization - Find pricing for Virtualization Engine
    let openshiftCost = 0
    const redhatData = pricingData.pricing_data.find(vendor => vendor.vendor === 'Red Hat')
    const virtualizationEngine = redhatData?.products?.find(p => p.product_name === 'OpenShift Virtualization Engine')
    
    if (virtualizationEngine?.pricing_models?.[0]?.subscriptions) {
      const subscription = virtualizationEngine.pricing_models[0].subscriptions.find(
        s => s.support_level.toLowerCase() === (supportLevel === 'premium' ? 'premium' : 'standard')
      )
      if (subscription) {
        openshiftCost = subscription.price * hosts[0] // Per bare metal node
      }
    }

    // VMware vSphere - Calculate based on CPU licensing
    let vsphereCost = 0
    const vmwareData = pricingData.pricing_data.find(vendor => vendor.vendor === 'VMware')
    const vsphereProduct = vmwareData?.products?.find(p => p.product_name === 'vSphere')
    
    if (vsphereProduct?.editions) {
      const edition = supportLevel === 'premium' ? 'Enterprise Plus' : 'Standard'
      const pricing = vsphereProduct.editions.find(e => e.edition_name === edition)
      if (pricing) {
        // Apply 72-core minimum per the licensing requirements
        const billableCores = Math.max(totalCores, 72)
        const corePrice = pricing.pricing.price / 16 // Convert per-CPU to per-core (assuming 16 cores per CPU)
        vsphereCost = billableCores * corePrice
      }
    }

    return [
      {
        platform: 'Harvester',
        cost: harvesterCost + (supportLevel !== 'none' ? harvesterSupportCost : 0),
        color: platformColors.Harvester,
        details: supportLevel !== 'none' ? 'Includes SUSE support' : 'Open source (no support)'
      },
      {
        platform: 'OpenShift',
        cost: openshiftCost,
        color: platformColors.OpenShift,
        details: 'Virtualization Engine license'
      },
      {
        platform: 'vSphere',
        cost: vsphereCost,
        color: platformColors.vSphere,
        details: `${supportLevel === 'premium' ? 'Enterprise Plus' : 'Standard'} edition`
      }
    ].sort((a, b) => a.cost - b.cost)
  }

  const costData = calculateCosts()
  const totalCores = hosts[0] * socketsPerHost[0] * 16

  if (loading) {
    return (
      <section id="calculator" className="py-20 bg-muted/20">
        <div className="max-w-6xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center">
            <div className="animate-pulse text-lg">Loading cost calculator...</div>
          </div>
        </div>
      </section>
    )
  }

  return (
    <section id="calculator" className="py-20 bg-muted/20">
      <div className="max-w-6xl mx-auto px-4 sm:px-6 lg:px-8">
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          whileInView={{ opacity: 1, y: 0 }}
          viewport={{ once: true }}
          className="text-center mb-16"
        >
          <h2 className="text-3xl md:text-4xl font-bold mb-4">
            Advanced <span className="text-accent">Cost Calculator</span>
          </h2>
          <p className="text-xl text-muted-foreground max-w-3xl mx-auto">
            Calculate and compare annual licensing costs for your infrastructure
          </p>
        </motion.div>

        <div className="grid lg:grid-cols-2 gap-8">
          {/* Input Controls */}
          <motion.div
            initial={{ opacity: 0, x: -30 }}
            whileInView={{ opacity: 1, x: 0 }}
            viewport={{ once: true }}
            className="space-y-6"
          >
            <Card className="bg-card/50 backdrop-blur-sm border-border">
              <CardHeader>
                <CardTitle className="flex items-center space-x-2">
                  <Calculator className="h-5 w-5 text-accent" />
                  <span>Infrastructure Configuration</span>
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-6">
                {/* Number of Hosts */}
                <div className="space-y-3">
                  <div className="flex items-center justify-between">
                    <Label>Number of Hosts</Label>
                    <span className="text-sm font-medium bg-accent/20 px-2 py-1 rounded">
                      {hosts[0]}
                    </span>
                  </div>
                  <Slider
                    value={hosts}
                    onValueChange={setHosts}
                    max={20}
                    min={1}
                    step={1}
                    className="w-full"
                  />
                  <p className="text-xs text-muted-foreground">
                    Physical servers in your cluster
                  </p>
                </div>

                {/* Sockets per Host */}
                <div className="space-y-3">
                  <div className="flex items-center justify-between">
                    <Label>CPU Sockets per Host</Label>
                    <span className="text-sm font-medium bg-accent/20 px-2 py-1 rounded">
                      {socketsPerHost[0]}
                    </span>
                  </div>
                  <Slider
                    value={socketsPerHost}
                    onValueChange={setSocketsPerHost}
                    max={4}
                    min={1}
                    step={1}
                    className="w-full"
                  />
                  <p className="text-xs text-muted-foreground">
                    CPU sockets per physical server
                  </p>
                </div>

                {/* Support Level */}
                <div className="space-y-3">
                  <Label>Support Level</Label>
                  <Select value={supportLevel} onValueChange={setSupportLevel}>
                    <SelectTrigger>
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="none">No Support (Harvester only)</SelectItem>
                      <SelectItem value="standard">Standard (8x5)</SelectItem>
                      <SelectItem value="premium">Premium (24x7)</SelectItem>
                    </SelectContent>
                  </Select>
                  <p className="text-xs text-muted-foreground">
                    Level of vendor support required
                  </p>
                </div>

                {/* Configuration Summary */}
                <div className="p-4 bg-muted/30 rounded-lg space-y-2">
                  <h4 className="font-medium text-sm">Configuration Summary</h4>
                  <div className="grid grid-cols-2 gap-4 text-xs">
                    <div>
                      <span className="text-muted-foreground">Total Hosts:</span>
                      <span className="ml-2 font-medium">{hosts[0]}</span>
                    </div>
                    <div>
                      <span className="text-muted-foreground">Total Sockets:</span>
                      <span className="ml-2 font-medium">{hosts[0] * socketsPerHost[0]}</span>
                    </div>
                    <div>
                      <span className="text-muted-foreground">Estimated Cores:</span>
                      <span className="ml-2 font-medium">{totalCores}</span>
                    </div>
                    <div>
                      <span className="text-muted-foreground">Support:</span>
                      <span className="ml-2 font-medium capitalize">{supportLevel}</span>
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>
          </motion.div>

          {/* Results */}
          <motion.div
            initial={{ opacity: 0, x: 30 }}
            whileInView={{ opacity: 1, x: 0 }}
            viewport={{ once: true }}
            className="space-y-6"
          >
            {/* Cost Comparison Chart */}
            <Card className="bg-card/50 backdrop-blur-sm border-border">
              <CardHeader>
                <CardTitle className="flex items-center space-x-2">
                  <TrendingUp className="h-5 w-5 text-accent" />
                  <span>Annual Cost Comparison</span>
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="h-64 w-full">
                  <ResponsiveContainer width="100%" height="100%">
                    <BarChart data={costData} margin={{ top: 20, right: 30, left: 20, bottom: 5 }}>
                      <XAxis 
                        dataKey="platform" 
                        tick={{ fontSize: 11, fill: 'hsl(var(--muted-foreground))' }}
                        tickLine={false}
                      />
                      <YAxis 
                        tick={{ fontSize: 10, fill: 'hsl(var(--muted-foreground))' }}
                        tickLine={false}
                        label={{ 
                          value: 'Annual Cost (USD)', 
                          angle: -90, 
                          position: 'insideLeft', 
                          style: { textAnchor: 'middle', fontSize: 11, fill: 'hsl(var(--muted-foreground))' } 
                        }}
                      />
                      <Tooltip 
                        contentStyle={{ 
                          backgroundColor: 'hsl(var(--card))', 
                          border: '1px solid hsl(var(--border))',
                          borderRadius: '8px',
                          fontSize: '12px'
                        }}
                        formatter={(value: any) => [`$${value?.toLocaleString() || 0}`, 'Annual Cost']}
                      />
                      <Bar dataKey="cost" radius={[4, 4, 0, 0]}>
                        {costData.map((entry, index) => (
                          <Cell key={`cell-${index}`} fill={entry.color} />
                        ))}
                      </Bar>
                    </BarChart>
                  </ResponsiveContainer>
                </div>
              </CardContent>
            </Card>

            {/* Detailed Cost Breakdown */}
            <Card className="bg-card/50 backdrop-blur-sm border-border">
              <CardHeader>
                <CardTitle className="flex items-center space-x-2">
                  <DollarSign className="h-5 w-5 text-accent" />
                  <span>Cost Breakdown</span>
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  {costData.map((platform, index) => (
                    <motion.div
                      key={platform.platform}
                      initial={{ opacity: 0, y: 20 }}
                      whileInView={{ opacity: 1, y: 0 }}
                      viewport={{ once: true }}
                      transition={{ delay: index * 0.1 }}
                      className="flex items-center justify-between p-4 rounded-lg bg-muted/30 hover:bg-muted/50 transition-colors"
                    >
                      <div className="flex items-center space-x-3">
                        <div 
                          className="w-4 h-4 rounded-full" 
                          style={{ backgroundColor: platform.color }}
                        />
                        <div>
                          <h4 className="font-medium">{platform.platform}</h4>
                          <p className="text-xs text-muted-foreground">{platform.details}</p>
                        </div>
                      </div>
                      <div className="text-right">
                        <p className="text-lg font-semibold">
                          ${platform.cost?.toLocaleString() || 0}
                        </p>
                        <p className="text-xs text-muted-foreground">per year</p>
                      </div>
                    </motion.div>
                  ))}
                </div>
              </CardContent>
            </Card>

            {/* Cost Savings */}
            {costData.length > 1 && (
              <Card className="bg-gradient-to-r from-green-500/10 to-accent/10 border-green-500/50">
                <CardContent className="p-6">
                  <div className="flex items-start space-x-4">
                    <TrendingUp className="h-6 w-6 text-green-400 mt-1 flex-shrink-0" />
                    <div>
                      <h3 className="font-semibold mb-2">Potential Savings</h3>
                      <p className="text-sm text-muted-foreground">
                        Choosing {costData[0]?.platform} over {costData[costData.length - 1]?.platform} could save{' '}
                        <span className="text-green-400 font-semibold">
                          ${((costData[costData.length - 1]?.cost || 0) - (costData[0]?.cost || 0)).toLocaleString()}
                        </span>{' '}
                        annually ({Math.round(((costData[costData.length - 1]?.cost || 0) - (costData[0]?.cost || 0)) / (costData[costData.length - 1]?.cost || 1) * 100)}% reduction)
                      </p>
                    </div>
                  </div>
                </CardContent>
              </Card>
            )}
          </motion.div>
        </div>

        <motion.div
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          viewport={{ once: true }}
          transition={{ delay: 0.6 }}
          className="mt-8"
        >
          <Card className="bg-gradient-to-r from-amber-500/10 to-orange-500/10 border-amber-500/50">
            <CardContent className="p-6">
              <div className="flex items-start space-x-4">
                <AlertCircle className="h-6 w-6 text-amber-400 mt-1 flex-shrink-0" />
                <div>
                  <h3 className="font-semibold mb-2">Cost Calculation Notes</h3>
                  <ul className="space-y-1 text-sm text-muted-foreground">
                    <li>• Harvester is open-source and free to use, costs shown include optional SUSE support</li>
                    <li>• OpenShift pricing based on Virtualization Engine SKU for VM-only workloads</li>
                    <li>• VMware pricing includes 72-core minimum licensing requirement</li>
                    <li>• Actual costs may vary based on specific requirements and vendor negotiations</li>
                    <li>• Consider additional costs like hardware, storage, networking, and operational expenses</li>
                  </ul>
                </div>
              </div>
            </CardContent>
          </Card>
        </motion.div>
      </div>
    </section>
  )
}
