{"name": "plotly.js", "version": "2.35.3", "description": "The open source javascript graphing library that powers plotly", "license": "MIT", "main": "./lib/index.js", "webpack": "./dist/plotly.js", "repository": {"type": "git", "url": "https://github.com/plotly/plotly.js.git"}, "bugs": {"url": "https://github.com/plotly/plotly.js/issues"}, "author": "Plotly, Inc.", "keywords": ["graphing", "plotting", "data", "visualization", "plotly"], "scripts": {"custom-bundle": "node tasks/custom_bundle.js", "bundle": "node tasks/bundle.js", "extra-bundles": "node tasks/extra_bundles.js", "locales": "node tasks/locales.js", "schema": "node tasks/schema.js", "stats": "node tasks/stats.js", "find-strings": "node tasks/find_locale_strings.js", "preprocess": "node tasks/preprocess.js", "use-draftlogs": "node tasks/use_draftlogs.js", "empty-draftlogs": "node tasks/empty_draftlogs.js", "empty-dist": "node tasks/empty_dist.js", "build": "npm run empty-dist && npm run preprocess && npm run find-strings && npm run bundle && npm run extra-bundles && npm run locales && npm run schema dist && npm run stats", "regl-codegen": "node devtools/regl_codegen/server.js", "cibuild": "npm run empty-dist && npm run preprocess && node tasks/cibundle.js", "lint": "npx @biomejs/biome lint", "lint-fix": "npx @biomejs/biome format ./test/image/mocks --write; npx @biomejs/biome lint --write || true", "pretest": "node tasks/pretest.js", "test-jasmine": "karma start test/jasmine/karma.conf.js", "test-mock": "node tasks/test_mock.js", "test-image": "node test/image/compare_pixels_test.js", "test-export": "node test/image/export_test.js", "test-syntax": "node tasks/test_syntax.js && npm run find-strings -- --no-output", "test-bundle": "node tasks/test_bundle.js", "test-amdefine": "node tasks/test_amdefine.js", "test-requirejs": "node tasks/test_requirejs.js", "test-plain-obj": "node tasks/test_plain_obj.js", "test": "npm run test-jasmine -- --nowatch && npm run test-bundle && npm run test-image && npm run test-export && npm run test-syntax && npm run lint", "b64": "python3 test/image/generate_b64_mocks.py && node devtools/test_dashboard/server.js", "mathjax3": "node devtools/test_dashboard/server.js --mathjax3", "mathjax3chtml": "node devtools/test_dashboard/server.js --mathjax3chtml", "strict": "node devtools/test_dashboard/server.js --strict", "start": "node devtools/test_dashboard/server.js", "baseline": "node test/image/make_baseline.js", "noci-baseline": "npm run cibuild && ./tasks/noci_test.sh image && git checkout dist && echo 'Please do not commit unless the change was expected!'", "preversion": "check-node-version --node 18 --npm 10 && npm-link-check && npm ls --prod --all", "version": "npm run build && git add -A lib dist build src/version.js", "postversion": "node -e \"console.log('Version bumped and committed. If ok, run: git push && git push --tags')\"", "postpublish": "node tasks/sync_packages.js", "postshrinkwrap": "chttps ."}, "browserify": {"transform": ["glslify"]}, "dependencies": {"@plotly/d3": "3.8.2", "@plotly/d3-sankey": "0.7.2", "@plotly/d3-sankey-circular": "0.33.1", "@plotly/mapbox-gl": "1.13.4", "@turf/area": "^7.1.0", "@turf/bbox": "^7.1.0", "@turf/centroid": "^7.1.0", "base64-arraybuffer": "^1.0.2", "canvas-fit": "^1.5.0", "color-alpha": "1.0.4", "color-normalize": "1.5.0", "color-parse": "2.0.0", "color-rgba": "2.1.1", "country-regex": "^1.1.0", "css-loader": "^7.1.2", "d3-force": "^1.2.1", "d3-format": "^1.4.5", "d3-geo": "^1.12.1", "d3-geo-projection": "^2.9.0", "d3-hierarchy": "^1.1.9", "d3-interpolate": "^3.0.1", "d3-time": "^1.1.0", "d3-time-format": "^2.2.3", "fast-isnumeric": "^1.1.4", "gl-mat4": "^1.2.0", "gl-text": "^1.4.0", "has-hover": "^1.0.1", "has-passive-events": "^1.0.0", "is-mobile": "^4.0.0", "maplibre-gl": "^4.5.2", "mouse-change": "^1.4.0", "mouse-event-offset": "^3.0.2", "mouse-wheel": "^1.2.0", "native-promise-only": "^0.8.1", "parse-svg-path": "^0.1.2", "point-in-polygon": "^1.1.0", "polybooljs": "^1.2.2", "probe-image-size": "^7.2.3", "regl": "npm:@plotly/regl@^2.1.2", "regl-error2d": "^2.0.12", "regl-line2d": "^3.1.3", "regl-scatter2d": "^3.3.1", "regl-splom": "^1.0.14", "strongly-connected-components": "^1.0.1", "style-loader": "^4.0.0", "superscript-text": "^1.0.0", "svg-path-sdf": "^1.1.3", "tinycolor2": "^1.4.2", "to-px": "1.0.1", "topojson-client": "^3.1.0", "webgl-context": "^2.2.0", "world-calendars": "^1.0.3"}, "devDependencies": {"@babel/core": "^7.25.2", "@babel/plugin-transform-modules-commonjs": "^7.23.3", "@babel/preset-env": "^7.23.9", "@biomejs/biome": "1.8.3", "amdefine": "^1.0.1", "babel-loader": "^9.1.3", "browserify-transform-tools": "^1.7.0", "bubleify": "^2.0.0", "canvas": "^2.11.2", "check-node-version": "^4.2.1", "chttps": "^1.0.6", "deep-equal": "^2.2.3", "ecstatic": "^4.1.4", "extra-iterable": "^2.5.22", "falafel": "^2.2.5", "fs-extra": "^10.1.0", "fuse.js": "^6.6.2", "glob": "^10.4.5", "gzip-size": "^6.0.0", "ify-loader": "^1.1.0", "into-stream": "^6.0.0", "jasmine": "3.5.0", "jasmine-core": "3.5.0", "jsdom": "^24.1.1", "karma": "^6.4.2", "karma-chrome-launcher": "^3.2.0", "karma-firefox-launcher": "^2.1.2", "karma-ie-launcher": "^1.0.0", "karma-jasmine": "^3.3.1", "karma-jasmine-spec-tags": "^1.3.0", "karma-spec-reporter": "^0.0.36", "karma-verbose-reporter": "^0.0.8", "karma-viewport": "1.0.2", "karma-webpack": "^5.0.0", "lodash": "^4.17.21", "madge": "^8.0.0", "mathjax-v2": "npm:mathjax@2.7.5", "mathjax-v3": "npm:mathjax@^3.2.2", "minify-stream": "^2.1.0", "node-polyfill-webpack-plugin": "^4.0.0", "npm-link-check": "^5.0.1", "open": "^8.4.2", "pixelmatch": "^5.3.0", "prepend-file": "^2.0.1", "prettysize": "^2.0.0", "raw-loader": "^4.0.2", "read-last-lines": "^1.8.0", "run-series": "^1.1.9", "sane-topojson": "^4.0.0", "sass": "^1.77.8", "stream-browserify": "^3.0.0", "through2": "^4.0.2", "transform-loader": "^0.2.4", "true-case-path": "^2.2.1", "virtual-webgl": "^1.0.6", "webpack": "^5.90.1", "webpack-cli": "^5.1.4"}}