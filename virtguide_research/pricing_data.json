{"report_metadata": {"objective": "Current market pricing of all OpenShift SKUs (Kubernetes Engine, Container Platform, Platform Plus, Virtualization Engine, Data Foundation), VMware vSphere licenses, and SUSE support for Rancher Harvester for VirtGuide cost calculator integration", "report_date": "2025-07-09", "currency_note": "Prices are listed in the currency provided by the source material (EUR or USD). Conversions are not applied."}, "pricing_data": [{"vendor": "Red Hat", "licensing_summary": {"model_shift": "Red Hat has transitioned from a socket-based to a physical core-based licensing model for most OpenShift products.", "subscription_types": [{"type": "Core-pair", "definition": "2 physical cores or 4 virtual CPUs (vCPUs). Can be aggregated and span multiple servers or VMs. Not applicable for OpenShift Virtualization Engine."}, {"type": "Bare metal socket-pair", "definition": "Covers 1-2 sockets with a maximum of 128 cores on a single physical server. Cannot be split across servers. This is the only subscription type for OpenShift Virtualization Engine."}], "support_levels": [{"level": "Standard", "availability": "8x5, Mon<PERSON><PERSON><PERSON>"}, {"level": "Premium", "availability": "24x7"}]}, "products": [{"product_name": "OpenShift Virtualization Engine", "description": "A streamlined solution for deploying, managing, and scaling Virtual Machines (VMs) on bare metal.", "pricing_models": [{"model_name": "Bare Metal Node Subscription", "unit": "1-2 sockets, up to 128 cores", "subscriptions": [{"support_level": "Premium", "duration_years": 1, "price": 3000, "currency": "USD", "sku": "MW03815"}, {"support_level": "Standard", "duration_years": 1, "price": 2000, "currency": "USD", "sku": "MW03816"}]}]}, {"product_name": "OpenShift Kubernetes Engine", "description": "A hybrid cloud, enterprise Kubernetes runtime engine that provides core OpenShift functionality.", "pricing_models": [{"model_name": "Core-pair Subscription", "unit": "2 Cores or 4 vCPUs", "subscriptions": [{"support_level": "Premium", "duration_years": 1, "price": 1050, "currency": "USD", "sku": "MCT3822"}, {"support_level": "Standard", "duration_years": 1, "price": 695, "currency": "USD", "sku": "MCT3823"}]}, {"model_name": "Bare Metal Node Subscription", "unit": "1-2 sockets, up to 128 cores", "subscriptions": [{"support_level": "Premium", "duration_years": 1, "price": 13200, "currency": "USD", "sku": "MW04379"}, {"support_level": "Standard", "duration_years": 1, "price": 8712, "currency": "USD", "sku": "MW04380"}]}]}, {"product_name": "OpenShift Container Platform", "description": "A full-featured hybrid cloud, enterprise Kubernetes application platform to build, deploy, and run applications.", "pricing_models": [{"model_name": "Core-pair Subscription", "unit": "2 Cores or 4 vCPUs", "subscriptions": [{"support_level": "Premium", "duration_years": 1, "price": 4200, "currency": "USD", "sku": "MCT2735"}, {"support_level": "Standard", "duration_years": 1, "price": 2840, "currency": "USD", "sku": "MCT2736"}]}, {"model_name": "Bare Metal Node Subscription", "unit": "1-2 sockets, up to 128 cores", "subscriptions": [{"support_level": "Premium", "duration_years": 1, "price": 39600, "currency": "USD", "sku": "MW04365"}, {"support_level": "Standard", "duration_years": 1, "price": 26400, "currency": "USD", "sku": "MW04366"}]}]}, {"product_name": "OpenShift Platform Plus", "description": "A comprehensive hybrid cloud platform to build, deploy, run, and manage applications at scale across multiple clusters.", "pricing_models": [{"model_name": "Core-pair Subscription", "unit": "2 Cores or 4 vCPUs", "subscriptions": [{"support_level": "Premium", "duration_years": 1, "price": 5250, "currency": "USD", "sku": "MW01621"}, {"support_level": "Standard", "duration_years": 1, "price": 3520, "currency": "USD", "sku": "MW01622"}]}, {"model_name": "Bare Metal Node Subscription", "unit": "1-2 sockets, up to 128 cores", "subscriptions": [{"support_level": "Premium", "duration_years": 1, "price": 49500, "currency": "USD", "sku": "MW04348"}, {"support_level": "Standard", "duration_years": 1, "price": 33165, "currency": "USD", "sku": "MW04349"}]}]}, {"product_name": "OpenShift Data Foundation", "description": "Integrated persistent storage and data services for containers.", "pricing_models": [{"model_name": "Core-based Subscription", "unit": "Physical Cores", "subscriptions": [], "pricing_notes": "Subscriptions are based on physical cores and are stackable. Specific pricing per core was not available in the provided research materials."}]}]}, {"vendor": "VMware", "licensing_summary": {"model_shift": "VMware has discontinued perpetual licenses, moving exclusively to a core-based subscription model.", "minimum_core_requirement": "Effective April 10, 2025, a 72-core minimum per license instance applies to all product purchases. The previous 16-core-per-CPU minimum is still used for baseline calculation, but the total purchase must meet the 72-core minimum."}, "products": [{"product_name": "vSphere", "editions": [{"edition_name": "Standard", "pricing": {"model_name": "Subscription", "unit": "per CPU per year", "price": 995, "currency": "USD", "duration_years": 1, "pricing_notes": "This 'per CPU' pricing is subject to the 72-core minimum per purchase order. The actual cost is calculated based on the total number of cores."}}, {"edition_name": "Enterprise Plus", "pricing": {"model_name": "Subscription", "unit": "per CPU per year", "price": 4395, "currency": "USD", "duration_years": 1, "pricing_notes": "This 'per CPU' pricing is subject to the 72-core minimum per purchase order. The actual cost is calculated based on the total number of cores."}}]}, {"product_name": "VMware vSphere Foundation (VVF)", "description": "A bundled offering that includes vSphere and other components.", "included_features": [{"feature": "vSAN Capacity", "details": "Includes 0.25 TiB (100 GiB) of vSAN capacity per licensed core. Capacity can be aggregated across VVF core licenses."}], "pricing_notes": "Specific pricing for the VVF bundle was not available in the provided research materials."}, {"product_name": "VMware Cloud Foundation (VCF)", "description": "A comprehensive bundled offering for hybrid cloud infrastructure.", "included_features": [{"feature": "vSAN Capacity", "details": "Includes 1 TiB of vSAN capacity per licensed core. Capacity can be aggregated across VCF core licenses."}], "pricing_notes": "Specific pricing for the VCF bundle was not available in the provided research materials."}]}, {"vendor": "SUSE", "licensing_summary": {"model": "SUSE uses a unified, node-based, tiered pricing model for its Enterprise Container Management (ECM) portfolio, including Rancher Prime and Harvester.", "procurement": "Specific pricing is not publicly available and requires a direct quote from SUSE sales or a certified reseller."}, "products": [{"product_name": "Rancher Harvester", "description": "An open-source hyperconverged infrastructure (HCI) solution built on Kubernetes.", "pricing_notes": "Enterprise support for Harvester is not sold as a standalone product. It requires an active Rancher Prime subscription. The number of licensed Harvester nodes must be equal to the number of licensed Rancher Prime nodes."}, {"product_name": "Rancher <PERSON>", "description": "The enterprise subscription for SUSE's multi-cluster Kubernetes management platform, providing support for the entire ECM portfolio.", "pricing_models": [{"model_name": "Node-based Subscription", "unit": "per Node", "minimum_purchase": 5, "pricing_notes": "Pricing is tiered and volume-based. Specific costs are available only via a direct quote from SUSE sales or a reseller."}]}]}]}